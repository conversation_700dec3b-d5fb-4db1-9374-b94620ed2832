import { EventEmitter } from 'events';
import { 
  DownloadTask, 
  CourseResource, 
  TaskStatus, 
  DownloadProgress 
} from '../types';
import { TextbookDownloader, DownloadTaskStatus } from './TextbookDownloader';
import { FileOrganizer } from './FileOrganizer';

/**
 * 下载管理器配置
 */
export interface DownloadManagerConfig {
  maxConcurrentDownloads: number;
  autoRetry: boolean;
  maxRetries: number;
  retryDelay: number;
  enableNotifications: boolean;
}

/**
 * 下载统计信息
 */
export interface DownloadStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  activeTasks: number;
  totalDownloadedBytes: number;
  averageSpeed: number;
}

/**
 * 下载管理器事件
 */
export interface DownloadManagerEvents {
  'task-added': (task: DownloadTask) => void;
  'task-started': (task: DownloadTask) => void;
  'task-progress': (task: DownloadTask, progress: DownloadProgress) => void;
  'task-completed': (task: DownloadTask) => void;
  'task-failed': (task: DownloadTask, error: string) => void;
  'task-cancelled': (task: DownloadTask) => void;
  'queue-empty': () => void;
  'stats-updated': (stats: DownloadStats) => void;
}

/**
 * 下载管理器
 * 负责管理下载队列、任务调度和状态跟踪
 */
export class DownloadManager extends EventEmitter {
  private config: DownloadManagerConfig;
  private textbookDownloader: TextbookDownloader;
  private tasks: Map<string, DownloadTask> = new Map();
  private activeDownloads: Set<string> = new Set();
  private taskQueue: string[] = [];
  private isProcessing: boolean = false;

  private readonly defaultConfig: DownloadManagerConfig = {
    maxConcurrentDownloads: 3,
    autoRetry: true,
    maxRetries: 3,
    retryDelay: 5000, // 5 seconds
    enableNotifications: true
  };

  constructor(
    fileOrganizer: FileOrganizer,
    config?: Partial<DownloadManagerConfig>
  ) {
    super();
    this.config = { ...this.defaultConfig, ...config };
    this.textbookDownloader = new TextbookDownloader(fileOrganizer);
  }

  /**
   * 添加下载任务
   */
  addTask(resource: CourseResource): DownloadTask {
    const taskId = this.generateTaskId(resource);
    
    // 检查是否已存在相同任务
    if (this.tasks.has(taskId)) {
      const existingTask = this.tasks.get(taskId)!;
      if (existingTask.status === 'downloading' || existingTask.status === 'pending') {
        throw new Error('该资源已在下载队列中');
      }
    }

    const task: DownloadTask = {
      id: taskId,
      resource,
      status: 'pending',
      progress: 0,
      speed: 0,
      estimatedTime: 0,
      requiresAuth: resource.requiresAuth,
      createdAt: new Date(),
      updatedAt: new Date(),
      outputPath: '',
      retryCount: 0,
      maxRetries: this.config.maxRetries
    };

    this.tasks.set(taskId, task);
    this.taskQueue.push(taskId);

    this.emit('task-added', task);
    this.emitStatsUpdate();

    // 开始处理队列
    this.processQueue();

    return task;
  }

  /**
   * 批量添加下载任务
   */
  addBatchTasks(resources: CourseResource[]): DownloadTask[] {
    const tasks: DownloadTask[] = [];
    
    for (const resource of resources) {
      try {
        const task = this.addTask(resource);
        tasks.push(task);
      } catch (error) {
        console.warn(`添加任务失败: ${resource.title}`, error);
      }
    }

    return tasks;
  }

  /**
   * 暂停任务
   */
  pauseTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status === 'downloading') {
      this.textbookDownloader.cancelDownload(taskId);
      task.status = 'paused';
      task.updatedAt = new Date();
      this.activeDownloads.delete(taskId);
      
      this.emit('task-cancelled', task);
      this.emitStatsUpdate();
      
      // 继续处理队列中的其他任务
      this.processQueue();
    }
  }

  /**
   * 恢复任务
   */
  resumeTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status === 'paused') {
      task.status = 'pending';
      task.updatedAt = new Date();
      
      // 将任务重新加入队列
      if (!this.taskQueue.includes(taskId)) {
        this.taskQueue.unshift(taskId); // 优先处理恢复的任务
      }
      
      this.processQueue();
    }
  }

  /**
   * 取消任务
   */
  cancelTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status === 'downloading') {
      this.textbookDownloader.cancelDownload(taskId);
      this.activeDownloads.delete(taskId);
    }

    // 从队列中移除
    const queueIndex = this.taskQueue.indexOf(taskId);
    if (queueIndex !== -1) {
      this.taskQueue.splice(queueIndex, 1);
    }

    task.status = 'cancelled';
    task.updatedAt = new Date();

    this.emit('task-cancelled', task);
    this.emitStatsUpdate();

    // 继续处理队列
    this.processQueue();
  }

  /**
   * 重试任务
   */
  retryTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status === 'failed') {
      task.status = 'pending';
      task.retryCount = 0;
      task.error = undefined;
      task.updatedAt = new Date();
      
      // 将任务重新加入队列
      if (!this.taskQueue.includes(taskId)) {
        this.taskQueue.push(taskId);
      }
      
      this.processQueue();
    }
  }

  /**
   * 清除任务
   */
  clearTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      return;
    }

    // 如果任务正在下载，先取消
    if (task.status === 'downloading') {
      this.cancelTask(taskId);
    }

    // 从队列中移除
    const queueIndex = this.taskQueue.indexOf(taskId);
    if (queueIndex !== -1) {
      this.taskQueue.splice(queueIndex, 1);
    }

    // 删除任务
    this.tasks.delete(taskId);
    this.emitStatsUpdate();
  }

  /**
   * 处理下载队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.taskQueue.length > 0 && this.activeDownloads.size < this.config.maxConcurrentDownloads) {
        const taskId = this.taskQueue.shift();
        if (!taskId) continue;

        const task = this.tasks.get(taskId);
        if (!task || task.status !== 'pending') {
          continue;
        }

        // 开始下载任务
        this.startDownloadTask(task);
      }

      // 检查队列是否为空
      if (this.taskQueue.length === 0 && this.activeDownloads.size === 0) {
        this.emit('queue-empty');
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 开始下载任务
   */
  private async startDownloadTask(task: DownloadTask): Promise<void> {
    task.status = 'downloading';
    task.updatedAt = new Date();
    this.activeDownloads.add(task.id);

    this.emit('task-started', task);
    this.emitStatsUpdate();

    try {
      // 执行下载
      await this.textbookDownloader.downloadTextbook(
        task.resource,
        (progress: DownloadProgress) => {
          // 更新任务进度
          task.progress = progress.progress;
          task.speed = progress.speed;
          task.estimatedTime = progress.estimatedTime;
          task.updatedAt = new Date();

          this.emit('task-progress', task, progress);
        }
      );

      // 下载完成
      task.status = 'completed';
      task.progress = 100;
      task.updatedAt = new Date();

      this.emit('task-completed', task);
      
      if (this.config.enableNotifications) {
        this.showNotification(`下载完成: ${task.resource.title}`);
      }
    } catch (error) {
      // 下载失败
      const errorMessage = error instanceof Error ? error.message : '下载失败';
      task.error = errorMessage;
      task.retryCount++;
      task.updatedAt = new Date();

      // 检查是否需要自动重试
      if (this.config.autoRetry && task.retryCount < task.maxRetries) {
        task.status = 'pending';
        
        // 延迟后重新加入队列
        setTimeout(() => {
          this.taskQueue.push(task.id);
          this.processQueue();
        }, this.config.retryDelay);
      } else {
        task.status = 'failed';
        this.emit('task-failed', task, errorMessage);
      }
    } finally {
      this.activeDownloads.delete(task.id);
      this.emitStatsUpdate();
      
      // 继续处理队列中的其他任务
      setTimeout(() => this.processQueue(), 100);
    }
  }

  /**
   * 显示通知
   */
  private showNotification(message: string): void {
    // 在实际应用中，这里可以集成系统通知
    console.log(`[通知] ${message}`);
  }

  /**
   * 发送统计信息更新事件
   */
  private emitStatsUpdate(): void {
    const stats = this.getStats();
    this.emit('stats-updated', stats);
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(resource: CourseResource): string {
    return `download_${resource.id}_${Date.now()}`;
  }

  /**
   * 获取任务
   */
  getTask(taskId: string): DownloadTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): DownloadTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 根据状态获取任务
   */
  getTasksByStatus(status: TaskStatus): DownloadTask[] {
    return Array.from(this.tasks.values()).filter(task => task.status === status);
  }

  /**
   * 获取下载统计信息
   */
  getStats(): DownloadStats {
    const tasks = Array.from(this.tasks.values());
    const completedTasks = tasks.filter(t => t.status === 'completed');
    const failedTasks = tasks.filter(t => t.status === 'failed');
    const activeTasks = tasks.filter(t => t.status === 'downloading');

    const totalDownloadedBytes = completedTasks.reduce((sum, task) => {
      return sum + (task.resource.metadata?.fileSize || 0);
    }, 0);

    const averageSpeed = activeTasks.length > 0 
      ? activeTasks.reduce((sum, task) => sum + task.speed, 0) / activeTasks.length
      : 0;

    return {
      totalTasks: tasks.length,
      completedTasks: completedTasks.length,
      failedTasks: failedTasks.length,
      activeTasks: activeTasks.length,
      totalDownloadedBytes,
      averageSpeed
    };
  }

  /**
   * 清除所有已完成的任务
   */
  clearCompletedTasks(): void {
    const completedTaskIds: string[] = [];
    
    for (const [taskId, task] of this.tasks.entries()) {
      if (task.status === 'completed') {
        completedTaskIds.push(taskId);
      }
    }

    completedTaskIds.forEach(taskId => {
      this.tasks.delete(taskId);
    });

    this.emitStatsUpdate();
  }

  /**
   * 暂停所有任务
   */
  pauseAll(): void {
    const downloadingTasks = this.getTasksByStatus('downloading');
    downloadingTasks.forEach(task => this.pauseTask(task.id));
  }

  /**
   * 恢复所有暂停的任务
   */
  resumeAll(): void {
    const pausedTasks = this.getTasksByStatus('paused');
    pausedTasks.forEach(task => this.resumeTask(task.id));
  }

  /**
   * 取消所有任务
   */
  cancelAll(): void {
    const activeTasks = [...this.getTasksByStatus('downloading'), ...this.getTasksByStatus('pending')];
    activeTasks.forEach(task => this.cancelTask(task.id));
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<DownloadManagerConfig>): void {
    this.config = { ...this.config, ...config };
    this.textbookDownloader.updateConfig({
      maxConcurrentDownloads: this.config.maxConcurrentDownloads
    });
  }

  /**
   * 获取当前配置
   */
  getConfig(): DownloadManagerConfig {
    return { ...this.config };
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.cancelAll();
    this.removeAllListeners();
    this.tasks.clear();
    this.taskQueue = [];
    this.activeDownloads.clear();
  }
}

export default DownloadManager;
