import * as ffmpeg from 'fluent-ffmpeg';
import * as ffmpegInstaller from '@ffmpeg-installer/ffmpeg';
import * as fs from 'fs-extra';
import * as path from 'path';
import { EventEmitter } from 'events';
import { 
  VideoMergeOptions, 
  DownloadProgress,
  FFmpegError,
  FileError 
} from '../types';

// 设置FFmpeg路径
ffmpeg.setFfmpegPath(ffmpegInstaller.path);

/**
 * 视频合并器配置
 */
export interface VideoMergerConfig {
  tempDir: string;
  deleteSegments: boolean;
  outputFormat: 'mp4' | 'avi' | 'mkv';
  videoCodec: string;
  audioCodec: string;
  quality: string;
}

/**
 * 合并进度信息
 */
export interface MergeProgress {
  phase: 'preparing' | 'merging' | 'finalizing' | 'completed';
  progress: number; // 0-100
  currentTime: number; // 当前处理时间（秒）
  totalTime: number; // 总时长（秒）
  speed: number; // 处理速度倍数
  fps: number; // 当前帧率
  message: string;
}

/**
 * 视频片段合并器
 * 使用FFmpeg将多个视频片段合并为单个视频文件
 */
export class VideoMerger extends EventEmitter {
  private config: VideoMergerConfig;

  private readonly defaultConfig: VideoMergerConfig = {
    tempDir: path.join(process.cwd(), 'temp'),
    deleteSegments: true,
    outputFormat: 'mp4',
    videoCodec: 'libx264',
    audioCodec: 'aac',
    quality: 'medium'
  };

  constructor(config?: Partial<VideoMergerConfig>) {
    super();
    this.config = { ...this.defaultConfig, ...config };
  }

  /**
   * 合并视频片段
   * @param segmentPaths 视频片段文件路径数组
   * @param outputPath 输出文件路径
   * @param options 合并选项
   */
  async mergeSegments(
    segmentPaths: string[], 
    outputPath: string, 
    options?: Partial<VideoMergeOptions>
  ): Promise<void> {
    const mergeOptions: VideoMergeOptions = {
      outputPath,
      format: 'mp4',
      deleteSegments: this.config.deleteSegments,
      ...options
    };

    try {
      // 验证输入文件
      await this.validateSegments(segmentPaths);

      // 确保输出目录存在
      await fs.ensureDir(path.dirname(outputPath));

      // 创建临时目录
      const tempDir = await this.createTempDir();

      this.emit('progress', {
        phase: 'preparing',
        progress: 0,
        currentTime: 0,
        totalTime: 0,
        speed: 0,
        fps: 0,
        message: '准备合并视频片段...'
      } as MergeProgress);

      // 使用FFmpeg合并视频
      await this.performMerge(segmentPaths, outputPath, mergeOptions, tempDir);

      // 验证输出文件
      await this.validateOutput(outputPath);

      // 清理临时文件
      if (mergeOptions.deleteSegments) {
        await this.cleanupSegments(segmentPaths);
      }
      await this.cleanupTempDir(tempDir);

      this.emit('progress', {
        phase: 'completed',
        progress: 100,
        currentTime: 0,
        totalTime: 0,
        speed: 0,
        fps: 0,
        message: '视频合并完成'
      } as MergeProgress);

      this.emit('completed', outputPath);
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 执行实际的视频合并操作
   */
  private async performMerge(
    segmentPaths: string[], 
    outputPath: string, 
    options: VideoMergeOptions,
    tempDir: string
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 创建文件列表文件（用于concat demuxer）
        const fileListPath = path.join(tempDir, 'filelist.txt');
        const fileListContent = segmentPaths
          .map(segmentPath => `file '${segmentPath.replace(/'/g, "'\\''")}'`)
          .join('\n');
        
        fs.writeFileSync(fileListPath, fileListContent);

        // 创建FFmpeg命令
        const command = ffmpeg()
          .input(fileListPath)
          .inputOptions(['-f', 'concat', '-safe', '0'])
          .videoCodec(this.config.videoCodec)
          .audioCodec(this.config.audioCodec)
          .format(options.format)
          .output(outputPath);

        // 设置质量参数
        this.applyQualitySettings(command, this.config.quality);

        // 监听进度事件
        command.on('progress', (progress) => {
          const mergeProgress: MergeProgress = {
            phase: 'merging',
            progress: Math.min(progress.percent || 0, 99),
            currentTime: this.parseTime(progress.timemark),
            totalTime: 0, // FFmpeg不总是提供总时长
            speed: progress.currentKbps ? progress.currentKbps / 1000 : 0,
            fps: progress.currentFps || 0,
            message: `正在合并视频... ${Math.round(progress.percent || 0)}%`
          };
          this.emit('progress', mergeProgress);
        });

        // 监听错误事件
        command.on('error', (error) => {
          reject(new FFmpegError(`视频合并失败: ${error.message}`));
        });

        // 监听完成事件
        command.on('end', () => {
          resolve();
        });

        // 开始处理
        command.run();
      } catch (error) {
        reject(new FFmpegError(`创建FFmpeg命令失败: ${error instanceof Error ? error.message : '未知错误'}`));
      }
    });
  }

  /**
   * 应用质量设置
   */
  private applyQualitySettings(command: ffmpeg.FfmpegCommand, quality: string): void {
    switch (quality) {
      case 'high':
        command.videoBitrate('2000k').audioBitrate('128k');
        break;
      case 'medium':
        command.videoBitrate('1000k').audioBitrate('96k');
        break;
      case 'low':
        command.videoBitrate('500k').audioBitrate('64k');
        break;
      default:
        // 使用默认设置
        break;
    }
  }

  /**
   * 验证视频片段文件
   */
  private async validateSegments(segmentPaths: string[]): Promise<void> {
    if (!segmentPaths || segmentPaths.length === 0) {
      throw new FileError('没有提供视频片段文件');
    }

    for (const segmentPath of segmentPaths) {
      if (!(await fs.pathExists(segmentPath))) {
        throw new FileError(`视频片段文件不存在: ${segmentPath}`);
      }

      const stats = await fs.stat(segmentPath);
      if (stats.size === 0) {
        throw new FileError(`视频片段文件为空: ${segmentPath}`);
      }
    }
  }

  /**
   * 验证输出文件
   */
  private async validateOutput(outputPath: string): Promise<void> {
    if (!(await fs.pathExists(outputPath))) {
      throw new FileError('输出文件创建失败');
    }

    const stats = await fs.stat(outputPath);
    if (stats.size === 0) {
      throw new FileError('输出文件为空');
    }
  }

  /**
   * 创建临时目录
   */
  private async createTempDir(): Promise<string> {
    const tempDir = path.join(this.config.tempDir, `merge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
    await fs.ensureDir(tempDir);
    return tempDir;
  }

  /**
   * 清理视频片段文件
   */
  private async cleanupSegments(segmentPaths: string[]): Promise<void> {
    for (const segmentPath of segmentPaths) {
      try {
        if (await fs.pathExists(segmentPath)) {
          await fs.remove(segmentPath);
        }
      } catch (error) {
        console.warn(`清理片段文件失败: ${segmentPath}`, error);
      }
    }
  }

  /**
   * 清理临时目录
   */
  private async cleanupTempDir(tempDir: string): Promise<void> {
    try {
      if (await fs.pathExists(tempDir)) {
        await fs.remove(tempDir);
      }
    } catch (error) {
      console.warn(`清理临时目录失败: ${tempDir}`, error);
    }
  }

  /**
   * 解析时间标记为秒数
   */
  private parseTime(timemark: string): number {
    if (!timemark) return 0;
    
    const parts = timemark.split(':');
    if (parts.length !== 3) return 0;
    
    const hours = parseInt(parts[0], 10) || 0;
    const minutes = parseInt(parts[1], 10) || 0;
    const seconds = parseFloat(parts[2]) || 0;
    
    return hours * 3600 + minutes * 60 + seconds;
  }

  /**
   * 获取视频信息
   */
  async getVideoInfo(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (error, metadata) => {
        if (error) {
          reject(new FFmpegError(`获取视频信息失败: ${error.message}`));
        } else {
          resolve(metadata);
        }
      });
    });
  }

  /**
   * 设置配置
   */
  updateConfig(config: Partial<VideoMergerConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

export default VideoMerger;
